import asyncio
import json
import os
import time
import requests
import aiohttp
import logging
import functools
import traceback

from service.base_service import BaseService

# 常量定义
MIFY_WORKFLOW_URL = "https://mify-be.pt.xiaomi.com/api/v1/workflows/run"
RETRIEVAL_URL_TEMPLATE = "https://mify-be.pt.xiaomi.com/api/v1/datasets/{dataset_id}/retrieve"

RETRIEVAL_NUM = 20
RETRIEVAL_THRESHOLD = 0
TIMEOUT_MIFY_RETRIEVAL = int(os.environ.get("TIMEOUT_MIFY_RETRIEVAL", 5))  # 秒
TIMEOUT_JSON_LLM = int(os.environ.get("TIMEOUT_JSON_LLM", 5))  # 秒
TIMEOUT_LLM = int(os.environ.get("TIMEOUT_LLM", 5))  # 秒
TIMEOUT_TRANSLATE = int(os.environ.get("TIMEOUT_TRANSLATE", 300))  # 秒

# 重试参数
MAX_RETRY_SIZE = 3
RETRY_DELAY = 1  # 秒


# 重试装饰器定义
# ToDo(hm): 装饰器怎么打日志？
def sync_retry(max_retry_size=3, delay=1,
               exceptions=(requests.exceptions.Timeout, requests.exceptions.RequestException)):
    """
    同步函数重试装饰器。
    """

    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            self = args[0]  # 假设第一个参数是类实例
            for attempt in range(1, max_retry_size + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    if attempt < max_retry_size:
                        # if logger:
                        #     logger.warning(
                        #         f"调用 {func.__name__} 出现 {e}，尝试次数 {attempt}/{max_retry_size}"
                        #     )
                        time.sleep(delay)
                    else:
                        # if logger:
                        #     logger.error(
                        #         f"在 {max_retry_size} 次尝试后，调用 {func.__name__} 仍然失败"
                        #     )
                        raise
                except Exception as e:
                    # if logger:
                    #     logger.error(
                    #         f"调用 {func.__name__} 时发生未预期的异常: {traceback.format_exc()}，尝试次数 {attempt}/{max_retry_size}"
                    #     )
                    if attempt < max_retry_size:
                        time.sleep(delay)
                    else:
                        raise
            return None

        return wrapper

    return decorator


def async_retry(max_retry_size=3, delay=1, exceptions=(aiohttp.ClientError, asyncio.TimeoutError)):
    """
    异步函数重试装饰器。
    """

    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            self = args[0]  # 假设第一个参数是类实例
            for attempt in range(1, max_retry_size + 1):
                try:
                    return await func(*args, **kwargs)
                except exceptions as e:
                    if attempt < max_retry_size:
                        # logger.warning(
                        #     f"调用 {func.__name__} 出现 {e}，尝试次数 {attempt}/{max_retry_size}"
                        # )
                        await asyncio.sleep(delay)
                    else:
                        # logger.error(
                        #     f"在 {max_retry_size} 次尝试后，调用 {func.__name__} 仍然失败"
                        # )
                        raise
                except Exception as e:
                    # logger.error(
                    #     f"调用 {func.__name__} 时发生未预期的异常: {traceback.format_exc()}，尝试次数 {attempt}/{max_retry_size}"
                    # )
                    if attempt < max_retry_size:
                        await asyncio.sleep(delay)
                    else:
                        raise
            return None

        return wrapper

    return decorator


class ModelManager(BaseService):
    def __init__(self, counter=None, timer=None, dataset_key=None):
        super().__init__(counter, timer)
        self._dataset_key = dataset_key

    # TODO(jxy) 由于流式调用出错，暂时不增加调用失败重试的逻辑
    # @async_retry(max_retry_size=MAX_RETRY_SIZE, delay=RETRY_DELAY)
    async def call_llm_with_stream(self, user_prompt, api_key, chat_request_id):
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }
        body = {
            "inputs": {"user_prompt": user_prompt},
            "response_mode": "streaming",
            "user": f"chat_request_id-{chat_request_id}",
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(
                    MIFY_WORKFLOW_URL,
                    headers=headers,
                    json=body,
                    timeout=aiohttp.ClientTimeout(total=TIMEOUT_JSON_LLM)
            ) as response:
                if response.status != 200:
                    error_msg = f"回答时底层大模型请求失败，状态码={response.status}，api_key={api_key}, 响应内容: {await response.text()}"
                    raise aiohttp.ClientResponseError(
                        response.request_info,
                        response.history,
                        status=response.status,
                        message=error_msg,
                    )

                first_response_received = False
                async for line in response.content:
                    if not first_response_received:
                        # 计算从发送请求到收到第一个响应的时间
                        first_response_received = True
                    yield line

    @async_retry(max_retry_size=MAX_RETRY_SIZE, delay=RETRY_DELAY)
    async def retrieve_knowledge_with_score(self, query, dataset_id, retrieval_num=RETRIEVAL_NUM):
        url = RETRIEVAL_URL_TEMPLATE.format(dataset_id=dataset_id)
        headers = {
            "Authorization": f"Bearer {self._dataset_key}",
            "Content-Type": "application/json",
        }
        body = {
            "query": query,
            "retrieval_model": {
                "search_method": "semantic_search",
                "reranking_enable": False,
                "reranking_mode": None,
                "reranking_model": {"reranking_provider_name": "", "reranking_model_name": ""},
                "weights": None,
                "top_k": retrieval_num,
                "score_threshold_enabled": False,
                "score_threshold": None,
            },
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(
                    url,
                    headers=headers,
                    json=body,
                    timeout=aiohttp.ClientTimeout(total=TIMEOUT_MIFY_RETRIEVAL)
            ) as response:
                response_json = await response.json()
                result = [
                    {
                        "content": record["segment"]["content"],
                        "score": record["score"]
                    }
                    for record in response_json.get("records", [])
                ]
                return result

    @async_retry(max_retry_size=MAX_RETRY_SIZE, delay=RETRY_DELAY)
    async def retrieve_knowledge_from_mify(self, query, dataset_id, retrieval_num=RETRIEVAL_NUM,
                                           retrieval_threshold=RETRIEVAL_THRESHOLD):
        url = RETRIEVAL_URL_TEMPLATE.format(dataset_id=dataset_id)
        headers = {
            "Authorization": f"Bearer {self._dataset_key}",
            "Content-Type": "application/json",
        }
        body = {
            "query": query,
            "retrieval_model": {
                "search_method": "semantic_search",
                "reranking_enable": False,
                "reranking_mode": None,
                "reranking_model": {"reranking_provider_name": "", "reranking_model_name": ""},
                "weights": None,
                "top_k": retrieval_num,
                "score_threshold_enabled": False,
                "score_threshold": None,
            },
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(
                    url,
                    headers=headers,
                    json=body,
                    timeout=aiohttp.ClientTimeout(total=TIMEOUT_MIFY_RETRIEVAL)
            ) as response:
                response_json = await response.json()
                result = []
                for record in response_json.get("records", []):
                    if record["score"] >= retrieval_threshold:
                        result.append(record["segment"]["content"])
                return result

    @async_retry(max_retry_size=MAX_RETRY_SIZE, delay=RETRY_DELAY)
    async def call_llm_with_json(self, prompt, api_key, chat_request_id="unknown"):
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }
        body = {
            "inputs": {"user_prompt": prompt},
            "response_mode": "blocking",
            "user": f"chat_request_id-{chat_request_id}",
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(
                    MIFY_WORKFLOW_URL,
                    headers=headers,
                    data=json.dumps(body),
                    timeout=aiohttp.ClientTimeout(total=TIMEOUT_JSON_LLM)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    try:
                        answer = result["data"]["outputs"]["answer"]
                        total_tokens = result["data"]["total_tokens"]
                        return True, answer, total_tokens
                    except KeyError:
                        raise ValueError(f"响应数据格式错误，无法解析返回数据 (data->outputs->answer): {result}")
                else:
                    error_text = await response.text()
                    raise aiohttp.ClientResponseError(
                        response.request_info,
                        response.history,
                        status=response.status,
                        message=f"底层大模型请求失败（json），状态码={response.status}, api_key={api_key}，响应内容: {error_text}",
                    )

    @sync_retry(max_retry_size=MAX_RETRY_SIZE, delay=RETRY_DELAY)
    def call_llm_sync(self, user_prompt, chat_request_id="=unknown", api_key="app-i3y8JTgXhIrU4QPKAasdwVal"):
        """同步版本的 call_llm 方法，增加重试逻辑"""
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }
        body = {
            "inputs": {"user_prompt": user_prompt},
            "response_mode": "blocking",
            "user": f"chat_request_id-{chat_request_id}",
        }

        response = requests.post(
            MIFY_WORKFLOW_URL,
            headers=headers,
            data=json.dumps(body),
            timeout=TIMEOUT_LLM
        )
        if response.status_code == 200:
            result = response.json()
            try:
                return True, result["data"]["outputs"]["answer"], result["data"]["total_tokens"]
            except KeyError:
                raise ValueError(f"call_llm_sync 返回的数据无法解析(data->outputs->answer): {result}")
        else:

            raise RuntimeError(
                f"底层大模型请求失败，状态码={response.status_code}, api_key={api_key}，响应内容: {response.text}"
            )

    @async_retry(max_retry_size=MAX_RETRY_SIZE, delay=RETRY_DELAY)
    async def translate_any2zn(self, content, api_key_translate, request_id="unknown"):
        headers = {
            "Authorization": f"Bearer {api_key_translate}",
            "Content-Type": "application/json",
        }
        data = {
            "inputs": {
                "content": content,
            },
            "response_mode": "blocking",
            "user": f"translate_request_id-{request_id}",
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(
                    MIFY_WORKFLOW_URL,
                    headers=headers,
                    json=data,
                    timeout=aiohttp.ClientTimeout(total=TIMEOUT_TRANSLATE)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    output_dict = result["data"]["outputs"]
                    return output_dict["answer"]
                else:
                    error_text = await response.text()
                    raise aiohttp.ClientResponseError(
                        response.request_info,
                        response.history,
                        status=response.status,
                        message=f"translate_any2zn_async 请求失败，状态码={response.status}，响应内容: {error_text}",
                    )

    @async_retry(max_retry_size=MAX_RETRY_SIZE, delay=RETRY_DELAY)
    async def call_llm_async(self, user_prompt, api_key, chat_request_id):
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
        }
        body = {
            "inputs": {"user_prompt": user_prompt},
            "response_mode": "blocking",
            "user": f"chat_request_id-{chat_request_id}",
        }

        async with aiohttp.ClientSession() as session:
            async with session.post(
                    MIFY_WORKFLOW_URL,
                    headers=headers,
                    data=json.dumps(body),
                    timeout=aiohttp.ClientTimeout(total=TIMEOUT_LLM)
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    try:
                        return True, result["data"]["outputs"]["answer"], result["data"]["total_tokens"]
                    except KeyError:
                        raise f"call_llm_async 返回的数据无法解析(data->outputs->answer): {result}"
                else:
                    raise aiohttp.ClientResponseError(
                        response.request_info,
                        response.history,
                        status=response.status,
                        message=f"底层大模型请求失败，状态码={response.status}, api_key={api_key}，响应内容: {await response.text()}",
                    )


if __name__ == '__main__':
    # 配置基本日志
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger("ModelManager")

    # 实例化 ModelManager
    model_manager = ModelManager(logger=logger)
    app_key = "app-pyMZ9rscnYtlmQbl72LskqXl"
    # 测试同步调用
    try:
        result = model_manager.call_llm_sync("hi", app_key)
        print(result)
    except Exception as e:
        print(f"同步调用失败: {e}")


    # 若需测试异步函数，可以使用以下代码：
    async def test_async():
        try:
            async for line in model_manager.call_llm_with_stream("hi", app_key):
                print(line)
        except Exception as e:
            print(f"异步流式调用失败: {e}")

        try:
            knowledge = await model_manager.retrieve_knowledge_with_score("query",
                                                                          "64083b09-9ad3-477e-a7b6-84174387084f")
            print(knowledge)
        except Exception as e:
            print(f"获取知识带分数失败: {e}")

        try:
            knowledge = await model_manager.retrieve_knowledge_from_mify("query",
                                                                         "64083b09-9ad3-477e-a7b6-84174387084f")
            print(knowledge)
        except Exception as e:
            print(f"从 Mify 获取知识失败: {e}")

        try:
            response = await model_manager.call_llm_with_json("prompt", app_key)
            print(response)
        except Exception as e:
            print(f"调用 LLM (JSON) 失败: {e}")

        try:
            translation = await model_manager.translate_any2zn("content", "app-tW5Sya7MmJieHKKD12TGMQFr")
            print(translation)
        except Exception as e:
            print(f"翻译失败: {e}")

        try:
            llm_async = await model_manager.call_llm_async("user_prompt", app_key)
            print(llm_async)
        except Exception as e:
            print(f"异步调用 LLM 失败: {e}")


    asyncio.run(test_async())
