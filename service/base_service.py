import time
from prometheus_client import Counter, Histogram

from core.schema.chat_request import Chat<PERSON><PERSON><PERSON>
from util.common_util import get_cur_millis


class BaseService:
    def __init__(self, counter: Counter, timer: Histogram):
        self._timer = timer
        self._counter = counter
        self._global_time_counter = {}
        self._global_token_counter = {}

    def log_elapse(self, label, chat_request: ChatRequest):
        elapsed_time = (get_cur_millis() - chat_request.request_receive_time) / 1000
        chat_request.logger.debug(f"{label}开始前耗时：{elapsed_time:.2f} 秒")

    # ToDo(hm): 这些 record 都没有打到日志里
    def record_execution_time(self, func_name: str, start_time: float):
        """
        记录函数的执行时间到 _global_time_counter 中。

        :param func_name: 函数名称
        :param received_time: 起始时间（秒）
        """
        count_time = time.time() - start_time
        original_name = func_name
        counter = 1
        while func_name in self._global_time_counter:
            func_name = f"{original_name}_{counter}"
            counter += 1
        self._global_time_counter[func_name] = count_time
        return f"记录 {func_name} 执行时间：{self._global_time_counter[func_name]:.2f} 秒"

    def get_execution_time_summary(self) -> str:
        """
        将 _global_time_counter 中的统计时间转换为字符串返回。

        :return: 包含函数名称和执行时间的字符串
        """
        # total_time = 0
        # summary_parts = []

        # for func_name, elapsed_time in self._global_time_counter.items():
        #     summary_parts.append(f"{func_name}: {elapsed_time:.2f} 秒")
        #     if func_name != "530_response_time":
        #         total_time += elapsed_time

        summary = ", ".join(
            [f"{func_name}: {elapsed_time:.2f} 秒" for func_name, elapsed_time in self._global_time_counter.items()]
        )
        # return f"执行时间统计: {summary}, 调用llm总计时间消耗: {total_time:.2f} 秒" if summary else "暂无执行时间统计"
        return f"执行时间统计: {summary}" if summary else "暂无执行时间统计"

    def record_token_usage(self, func_name: str, token_count: int):
        """
        记录函数的 token 消耗到 _global_token_counter 中。

        :param func_name: 函数名称
        :param token_count: 消耗的 token 数量
        """
        original_name = func_name
        counter = 1
        while func_name in self._global_token_counter:
            func_name = f"{original_name}_{counter}"
            counter += 1

        self._global_token_counter[func_name] = token_count
        return f"记录 {func_name} token 消耗：{token_count}，总计：{self._global_token_counter[func_name]}"

    def get_total_token_usage(self) -> int:
        return sum(self._global_token_counter.values())
