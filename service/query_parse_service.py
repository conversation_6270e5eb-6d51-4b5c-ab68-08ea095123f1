import time
import tomllib
from asyncio import TaskGroup
from typing import List

from core.schema.chat_request import ChatRequest, Language
from core.schema.chat_base import MessageType
from service.base_service import BaseService
from util.common_util import is_empty
from core.processor import normalize_item_name


class QueryParseService(BaseService):
    def __init__(self,
                 api_key_json,  # old api key for tag_question_second
                 api_key_json2,  # new api key for recognize_item_names
                 api_key_json3,  # new api key for tag_question_first
                 model_manager,
                 item_name_list,
                 normalized_item_name_list,
                 item_name_xiaomi_list,
                 prompts_path="config/prompts.toml"):

        super().__init__(None, None)
        self._api_key_json = api_key_json
        self._api_key_json2 = api_key_json2
        self._api_key_json3 = api_key_json3
        self._model_manager = model_manager
        self._item_name_list = item_name_list
        self._normalized_item_name_list = normalized_item_name_list
        self._item_name_xiaomi_list = item_name_xiaomi_list

        # 读取TOML配置文件
        with open(prompts_path, "rb") as f:
            self.prompts = tomllib.load(f)

        self.product_describe = self.prompts["PRODUCT_DESCRIBE"]["text"]
        self.first_tag_labels = self.prompts["FIRST_TAG_LABELS"]["labels"]
        self.second_tag_dict = self.prompts["SECOND_TAG_DICT"]

    async def filter_by_first_tags_430(self, chat_request: ChatRequest, tg: TaskGroup):
        chat_request_id = chat_request.request_id
        # TODO(jxy) 仅用于430版本的支持，后续将要删去
        # 这两个不适合放在一个 prompt 因为他们会相互干扰：打一级标签的时候会提示「当前的问题是针对 xxx」，但这个会影响实体的提取
        item_name_chunks = self.chunked(self._item_name_list, 100)
        recognize_item_names_tasks = []
        for chunk in item_name_chunks:
            task = tg.create_task(self.recognize_item_names_exact(
                chat_request.ending_message, chat_request_id, chunk
            ))
            recognize_item_names_tasks.append(task)
        tag_question_first_task = tg.create_task(self.tag_question_first(
            chat_request
        ))
        actual_item_names = []
        total_tokens_recognize_item_names = 0
        for task in recognize_item_names_tasks:
            batch_actual_item_names, batch_total_tokens = await task
            actual_item_names.extend(batch_actual_item_names)
            total_tokens_recognize_item_names += batch_total_tokens
        first_tags_normalized, total_tokens_tag_question_first, _ = await tag_question_first_task
        chat_request.logger.info(f"selected_item_name: {chat_request.item_name}")
        chat_request.logger.info(
            f"模型推理结果: actual_item_names===> {actual_item_names}; first tag===> {first_tags_normalized}"
        )

        is_not_in_scope, _ = self.should_filter_by_first_tags(first_tags_normalized)
        is_dynamic_change, _ = self.is_dynamic_change_tags(first_tags_normalized)

        query_filter_result = MessageType.TEXT
        if is_not_in_scope:
            query_filter_result = MessageType.NOT_IN_SCOPE

        if is_dynamic_change:
            query_filter_result = MessageType.DYNAMIC_CHANGE

        if not self._match_selected_item_names(chat_request.item_name_normalized, actual_item_names):
            query_filter_result = MessageType.NOT_MATCH_ITEM
        self.log_elapse("根据一级标签进行过滤返回", chat_request)
        total_tokens_filter_by_first_tags = total_tokens_tag_question_first + total_tokens_recognize_item_names
        return query_filter_result, actual_item_names, first_tags_normalized, total_tokens_filter_by_first_tags

    async def recognize_item_names(self, chat_request: ChatRequest, chat_request_id, is_fuzzy=False):
        total_tokens_recognize_item_names = 0
        item_name_chunks = self.chunked(self._item_name_list, 100)
        async with TaskGroup() as tg:
            recognize_exact_item_names_tasks = []
            for chunk in item_name_chunks:
                task = tg.create_task(self.recognize_item_names_exact(
                    chat_request.ending_message, chat_request_id, chunk
                ))
                recognize_exact_item_names_tasks.append(task)
            exact_item_names = []
            for task in recognize_exact_item_names_tasks:
                batch_exact_item_names, batch_total_tokens = await task
                exact_item_names.extend(batch_exact_item_names)
                total_tokens_recognize_item_names += batch_total_tokens

            if is_fuzzy:
                recognize_fuzzy_item_names_tasks = []
                for chunk in item_name_chunks:
                    task = tg.create_task(self.recognize_item_names_fuzzy(
                        chat_request.ending_message, chat_request_id, exact_item_names, chunk
                    ))
                    recognize_fuzzy_item_names_tasks.append(task)
                fuzzy_item_names = []
                for task in recognize_fuzzy_item_names_tasks:
                    batch_fuzzy_item_names, batch_total_tokens = await task
                    fuzzy_item_names.extend(batch_fuzzy_item_names)
                    total_tokens_recognize_item_names += batch_total_tokens

                if len(set(fuzzy_item_names) - set(exact_item_names)) > 0:
                    fuzzy_result = True
                else:
                    fuzzy_result = False
                fuzzy_item_names_with_exact = list(set(exact_item_names + fuzzy_item_names))
                chat_request.logger.debug(f"exact_item_names: {exact_item_names}")
                chat_request.logger.debug(f"fuzzy_item_names: {fuzzy_item_names}")
                chat_request.logger.debug(f"fuzzy_item_names_with_exact: {fuzzy_item_names_with_exact}")
                return fuzzy_result, exact_item_names, fuzzy_item_names, fuzzy_item_names_with_exact, total_tokens_recognize_item_names

        return exact_item_names, total_tokens_recognize_item_names

    async def recognize_item_names_for_chunk(self, chat_request: ChatRequest, tg: TaskGroup, mode="exact"):
        start_time = time.time()
        # 由于530版本的模糊识别仅用于问答类，而不用于双机对比类
        # 故，模糊识别机型仅会用到xiaomi的机型
        if mode == "exact":
            item_name_chunks = self.chunked(self._item_name_list, 100)
        elif mode == "fuzzy":
            item_name_chunks = self.chunked(self._item_name_xiaomi_list, 100)
        else:
            raise NotImplementedError
        recognize_item_names_tasks = []
        item_names = []
        total_tokens_recognize_item_names = 0

        for chunk in item_name_chunks:
            last_message = chat_request.str_message_list[-1]
            if mode == "exact":
                recognize_task = tg.create_task(
                    self.recognize_item_names_exact(last_message, chat_request.request_id, chunk))
            elif mode == "fuzzy":
                recognize_task = tg.create_task(
                    self.recognize_item_names_fuzzy(last_message, chat_request.request_id, chunk))
            else:
                raise NotImplementedError
            recognize_item_names_tasks.append(recognize_task)

        for task in recognize_item_names_tasks:
            batch_item_names, batch_total_tokens = await task
            item_names.extend(batch_item_names)
            total_tokens_recognize_item_names += batch_total_tokens

        return item_names, total_tokens_recognize_item_names, start_time

    async def recognize_language(self, chat_request: ChatRequest):
        start_time = time.time()
        candidates_for_language = Language.get_supported_languages()
        question_cn = chat_request.str_message_list[-1]
        prompt_template = self.prompts["prompts"]["recognize_language"]["text"]
        prompt = prompt_template.format(candidates_for_language=candidates_for_language, question_cn=question_cn)
        language = chat_request.language
        is_success, response, total_tokens_recognize_language = await self._model_manager.call_llm_with_json(
            prompt, self._api_key_json, chat_request.request_id)
        if is_success and "语言" in response:
            recognized_language = response["语言"]
            if not isinstance(recognized_language, list):
                recognized_language = [recognized_language]
            recognized_language = [cur_language for cur_language in recognized_language if
                                   cur_language in candidates_for_language]
            if len(recognized_language) > 0:
                language = Language.from_string(recognized_language[0])
        chat_request.logger.debug(f"recognize language: {language}")
        assert isinstance(language, Language)
        return language, total_tokens_recognize_language, start_time

    async def recognize_tags(self, chat_request: ChatRequest):
        start_time = time.time()
        history_messages = "\n".join(chat_request.str_message_list[:-1]) if len(
            chat_request.str_message_list) > 0 else "空"
        question_cn = chat_request.str_message_list[-1]
        label_part = "\n".join(self.second_tag_dict.keys())
        prompt_template = self.prompts["prompts"]["recognize_tags"]["text"]
        prompt = prompt_template.format(label_part=label_part, history_messages=history_messages,
                                        question_cn=question_cn)
        is_success, response, total_tokens_recognize_tage = await self._model_manager.call_llm_with_json(
            prompt, self._api_key_json, chat_request.request_id)
        second_tag_map_dict = dict()
        if not is_success:
            return second_tag_map_dict, total_tokens_recognize_tage, start_time

        second_tag_list = []
        if "提问标签" in response:
            second_tag_list = response["提问标签"]
        second_tag_map_dict = dict()
        for second_tag in second_tag_list:
            if second_tag in self.second_tag_dict:
                second_tag_map_dict[second_tag] = self.second_tag_dict[second_tag]
        return second_tag_map_dict, total_tokens_recognize_tage, start_time

    async def rewrite_query(self, chat_request: ChatRequest):
        start_time = time.time()
        history_messages = "\n".join(chat_request.str_message_list[:-1]) if len(
            chat_request.str_message_list) > 0 else "空"
        question_cn = chat_request.str_message_list[-1]
        prompt_template = self.prompts["prompts"]["rewrite_query"]["text"]
        prompt = prompt_template.format(history_messages=history_messages, question_cn=question_cn)
        is_success, response, total_tokens_rewrite_query = await self._model_manager.call_llm_with_json(
            prompt, self._api_key_json3, chat_request.request_id)
        chat_request.logger.info(f"rewrite_query promp===> {prompt}")
        chat_request.logger.info(f"rewrite_query response===> {response}")

        if not is_success:
            return question_cn, total_tokens_rewrite_query, start_time
        if "改写后的query" not in response:
            return question_cn, total_tokens_rewrite_query, start_time
        return response["改写后的query"], total_tokens_rewrite_query, start_time

    async def extract_item_names(self, chat_request: ChatRequest):
        start_time = time.time()
        question_cn = chat_request.str_message_list[-1]
        prompt_template = self.prompts["prompts"]["extract_item_names"]["text"]
        prompt = prompt_template.format(product_describe=self.product_describe, question_cn=question_cn)
        is_success, response, total_tokens_extract_item_names = await self._model_manager.call_llm_with_json(
            prompt, self._api_key_json2, chat_request.request_id)
        if not is_success:
            return list(), total_tokens_extract_item_names, start_time
        product_codes = self._get_product_codes_from_response_not_in_candicates(response, "产品类型")
        chat_request.logger.debug(f"extracted_item_names: {product_codes}")
        return product_codes, total_tokens_extract_item_names, start_time

    async def recognize_item_names_exact(self, question_cn, chat_request_id, item_name_list):
        prompt_template = self.prompts["prompts"]["recognize_item_names_exact"]["text"]
        prompt = prompt_template.format(item_names=",".join(item_name_list), product_describe=self.product_describe,
                                        question_cn=question_cn)
        is_success, response, total_tokens_recognize_item_names = await self._model_manager.call_llm_with_json(
            prompt, self._api_key_json2,
            chat_request_id)
        if not is_success:
            return list(), total_tokens_recognize_item_names

        product_codes = self._get_product_codes_from_response(response, "产品类型", item_name_list)
        return product_codes, total_tokens_recognize_item_names

    async def recognize_item_names_fuzzy(self, question_cn, chat_request_id, item_name_list):
        prompt_template = self.prompts["prompts"]["recognize_item_names_fuzzy"]["text"]
        prompt = prompt_template.format(item_names=",".join(item_name_list), product_describe=self.product_describe,
                                        question_cn=question_cn)
        is_success, response, total_tokens_recognize_item_names = await self._model_manager.call_llm_with_json(
            prompt, self._api_key_json2,
            chat_request_id)
        if not is_success:
            return list(), total_tokens_recognize_item_names

        product_codes = self._get_product_codes_from_response(response, "猜测可能提及的产品类型", item_name_list)
        return product_codes, total_tokens_recognize_item_names

    async def tag_question_first(self, chat_request: ChatRequest):
        start_time = time.time()
        history_messages = "\n".join(chat_request.str_message_list[:-1]) if len(
            chat_request.str_message_list) > 0 else "空"
        question_cn = chat_request.str_message_list[-1]
        prompt_template = self.prompts["prompts"]["tag_question_first"]["text"]
        prompt = prompt_template.format(history_messages=history_messages, question_cn=question_cn)
        is_success, response, total_tokens_tag_question_first = await self._model_manager.call_llm_with_json(
            prompt, self._api_key_json3, chat_request.request_id)
        if not is_success:
            return "其他意图", total_tokens_tag_question_first, start_time

        chat_request.logger.info(f"first tag promp===> {prompt}")
        chat_request.logger.info(f"first tag response===> {response}")
        first_tags = self._get_first_tags_from_response(response)
        first_tags_normalized = list()
        for tag in first_tags:
            normalized = self._normalize_tag(tag)
            if normalized is None:
                chat_request.logger.warning(f"大模型返回一个非法的 tag: {tag}")
                continue

            first_tags_normalized.append(normalized)
        if len(first_tags_normalized) == 0:
            first_tags_normalized = "其他意图"
        assert len(first_tags_normalized) > 0
        first_tags_normalized = self.get_first_intent_from_intends(first_tags_normalized)
        return first_tags_normalized, total_tokens_tag_question_first, start_time

    @staticmethod
    def chunked(iterable: List[str], n: int) -> List[List[str]]:
        """将列表分割为每组最多n个元素的子列表"""
        return [iterable[i:i + n] for i in range(0, len(iterable), n)]

    def _normalize_tag(self, tag):
        if "对比" in tag:
            tag = "双机对比"
        if "规格" in tag or "参数" in tag:
            tag = "规格参数"
        if "使用" in tag or "软件" in tag:
            tag = "软件使用"
        if "卖点" in tag:
            tag = "卖点咨询"
        if "缺点" in tag:
            tag = "缺点咨询"
        if "数码知识" in tag:
            tag = "数码知识"
        if "售前" in tag or "售后" in tag:
            tag = "售前售后咨询"
        if "时效" in tag:
            tag = "时效性问题"
        if "闲聊" in tag:
            tag = "闲聊对话"
        if "其他" in tag:
            tag = "其他意图"
        if "模糊" in tag:
            tag = "模糊意图"
        if tag not in ["规格参数", "软件使用", "卖点咨询", "缺点咨询",
                       "双机对比", "数码知识", "售前售后咨询",
                       "时效性问题", "闲聊对话", "其他意图", "模糊意图"]:
            tag = "其他意图"
        return tag

    def get_first_intent_from_intends(self, intent_names):
        # 从意图列表中根据优先级，拿出优先级最高的意图
        # TODO(jxy) 待补充
        if "双机对比" in intent_names:
            return "双机对比"

        return intent_names[0]

    @staticmethod
    def should_filter_by_first_tags(normalized_tags):
        return is_empty(normalized_tags), "非手机咨询问题"

    @staticmethod
    def is_dynamic_change_tags(normalized_tags):
        if "销售信息" in normalized_tags:
            return True, "属于动态变化中的问题"

        return False, "不属于动态变化中的问题"

    def _match_selected_item_names(self, expected_item_name, actual_item_names):
        if is_empty(actual_item_names):
            return True

        for name in actual_item_names:
            if expected_item_name == normalize_item_name(name):
                return True

        return False

    def _get_product_codes_from_response(self, response, key_words, item_name_list):
        product_codes = []
        if key_words in response:
            if not isinstance(response[key_words], list):
                response[key_words] = [response[key_words]]
            for code in response[key_words]:
                if code == "未知产品类型":
                    continue
                if code not in item_name_list or normalize_item_name(code) not in self._normalized_item_name_list:
                    continue
                product_codes.append(code)
        return product_codes

    def _get_product_codes_from_response_not_in_candicates(self, response, key_words):
        product_codes = []
        if key_words in response:
            if not isinstance(response[key_words], list):
                response[key_words] = [response[key_words]]
            for code in response[key_words]:
                if code == "未知产品类型":
                    continue
                if normalize_item_name(code) not in self._normalized_item_name_list:
                    product_codes.append(code)
        return product_codes

    def _get_first_tags_from_response(self, response):
        if "query分类" not in response:
            return []
        if not isinstance(response["query分类"], list):
            # 如果不是 list 则默认是配置
            query_labels = ["其他意图"]
            return query_labels
        query_labels = response["query分类"]
        return query_labels

    @property
    def item_name_list(self):
        return self._item_name_list
