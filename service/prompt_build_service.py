import tomllib
from core.schema.chat_request import ChatRequest
from core.schema.knowledge_source import KnowledgeSource
from service.base_service import BaseService
from util.common_util import is_empty, not_empty


class PromptBuildService(BaseService):
    def __init__(self, item_param_config_dict, model_manager, doc_dataset_id_dict, item_name_intro_dict,
                 prompts_path="config/prompts.toml"):
        super().__init__(None, None)
        self._doc_dataset_id_dict = doc_dataset_id_dict
        self._model_manager = model_manager
        self._item_name_intro_dict = item_name_intro_dict
        self._item_name_param_dict = item_param_config_dict

        # 读取TOML配置文件
        with open(prompts_path, "rb") as f:
            self.prompts = tomllib.load(f)

    def build_free_question_reject_prompt(self, chat_request: ChatRequest, answer_intent):
        item_name = "\n产品：" + chat_request.chat_history[-1].messages[0].selected_item.item_name if \
            chat_request.chat_history[-1].messages[0].selected_item else ""
        chat_context = "\n".join("\t" + message for message in chat_request.str_message_list)
        language = chat_request.language.name.capitalize()
        if chat_request.recognize_language is not None:
            language = chat_request.recognize_language.name.capitalize()
        prompt_template = self.prompts["prompts"]["free_question_reject"]["text"]
        prompt = prompt_template.format(chat_context=chat_context, answer_intent=answer_intent, item_name=item_name,
                                        question_cn=chat_request.ending_message, language=language)
        chat_request.logger.info(f"prompt={prompt}")
        return prompt

    def build_free_question_prompt(self, chat_request: ChatRequest):
        chat_ending_message = chat_request.ending_message
        item_name = "\促销员选定的产品：" + chat_request.chat_history[-1].messages[0].selected_item.item_name if \
            chat_request.chat_history[-1].messages[0].selected_item else ""
        chat_context = "\n".join("\t" + message for message in chat_request.str_message_list)
        language = chat_request.language.name.capitalize()
        if chat_request.recognize_language is not None:
            language = chat_request.recognize_language.name.capitalize()
        prompt_template = self.prompts["prompts"]["free_question"]["text"]
        prompt = prompt_template.format(language=language, chat_context=chat_context, question_cn=chat_ending_message,
                                        item_name=item_name)
        chat_request.logger.info(f"prompt={prompt}")
        return prompt

    # second_tag_map_dict is sub dict of SECOND_TAG_DICT
    def build_prompt(self, chat_request: ChatRequest, knowledge_dict):
        chat_request.logger.info(chat_request.str_message_list)
        # get ending round from chat_history
        item_name = chat_request.item_name
        chat_context = "\n".join("\t" + message for message in chat_request.str_message_list)
        knowledge_doc_all = knowledge_dict.get(KnowledgeSource.DOC)
        knowledge_doc = list()
        knowledge_faq = list()
        if not_empty(knowledge_doc_all):
            for data_dict in knowledge_doc_all:
                line = data_dict["content"]
                score = data_dict["score"]
                if is_empty(line):
                    continue

                split_data = line.split("\n")
                if is_empty(split_data):
                    continue

                stripped = "\n".join(split_data[1:-1])
                if split_data[0] == "<FAQ>":
                    knowledge_faq.append((score, stripped))
                    continue

                if split_data[0] == "<SALE TOOLS>":
                    knowledge_doc.append((score, stripped))
                    continue

        knowledge_xml_list = list()
        high_priority_list = list()
        if not_empty(knowledge_doc):
            knowledge_doc.sort(key=lambda x: x[0], reverse=True)
            chat_request.logger.debug(f"knowledge_doc={len(knowledge_doc)}")
            final_knowledge_doc = [element[1] for element in knowledge_doc]
            raw_content = final_knowledge_doc[0]
            knowledge_xml_list.append(f"<materi pelatihan>\n{raw_content}\n</materi pelatihan>")
        if not_empty(knowledge_faq):
            knowledge_faq.sort(key=lambda x: x[0], reverse=True)
            chat_request.logger.debug(f"knowledge_faq={len(knowledge_faq)}")
            final_knowledge_faq = [element[1] for element in knowledge_faq]
            raw_content = "\n".join(final_knowledge_faq[:3])
            knowledge_xml_list.append(f"<masalah umum>\n{raw_content}\n</masalah umum>")
        knowledge_intro = self._item_name_intro_dict.get(chat_request.item_name_normalized)
        if is_empty(knowledge_intro):
            chat_request.logger.warning(f"构建回答提示词时 {chat_request.item_name_normalized} 没有找到米网介绍文案")
        else:
            raw_content = "\n".join(knowledge_intro)
            knowledge_xml_list.append(f"<bahan promosi>\n{raw_content}\n</bahan promosi>")
            high_priority_list.append("<bahan promosi>")
        knowledge_param = self._item_name_param_dict.get(chat_request.item_name_normalized)
        if is_empty(knowledge_param):
            chat_request.logger.warning(f"构建回答提示词时 {chat_request.item_name_normalized} 没有找到米网参数")
        else:
            raw_content = "\n".join(knowledge_param)
            knowledge_xml_list.append(f"<informasi parameter>\n{raw_content}\n</informasi parameter>")
            high_priority_list.append("<informasi parameter>")
        if is_empty(knowledge_xml_list):
            return None

        priority_str = ""
        if not_empty(high_priority_list):
            priority_str = f"(优先使用{"、".join(high_priority_list)}中的信息)"
        knowledge_all_str = "\n".join(knowledge_xml_list)
        # 最后做了问题增强：对于 {item_name} 手机，我想问一下，{ending_message}
        language = chat_request.language.name.capitalize()
        if chat_request.recognize_language is not None:
            language = chat_request.recognize_language.name.capitalize()
        prompt_template = self.prompts["prompts"]["user_prompt"]["text"]
        prompt = prompt_template.format(item_name=item_name, knowledge_all_str=knowledge_all_str,
                                        chat_context=chat_context, priority_str=priority_str, language=language,
                                        question_cn=chat_request.ending_message)
        chat_request.logger.info(f"prompt len={len(prompt)}")
        chat_request.logger.info(f"prompt={prompt}")
        return prompt

    @staticmethod
    def enhance_retrieval_query(query, second_tag_map_dict):
        if is_empty(second_tag_map_dict):
            return query

        postfix = f"melibatkan aspek {", ".join(second_tag_map_dict.values())}"
        return f"{query}({postfix})"

    async def retrieve_doc_knowledge(self, chat_request, query, second_tag_map_dict, item_name_normalized):
        enhanced_query = self.enhance_retrieval_query(query, second_tag_map_dict)
        chat_request.logger.debug(f"用来召回知识的 query={enhanced_query}")
        # different spu use different dataset_id
        if item_name_normalized not in self._doc_dataset_id_dict:
            chat_request.logger.warning(f"没有找到{item_name_normalized}对应的知识（dataset id 缺失）")
            return list()

        doc_dataset_id = self._doc_dataset_id_dict[item_name_normalized]
        return await self._model_manager.retrieve_knowledge_with_score(enhanced_query, doc_dataset_id)
