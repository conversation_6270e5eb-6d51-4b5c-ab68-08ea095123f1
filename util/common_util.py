import json
import os
import re
import time

from loguru import logger

from util.string_util import to_json_str


def assert_eq(expected, actual):
    if expected != actual:
        raise RuntimeError(f"expected={expected}, actual={actual}")


def assert_not_eq(expected, actual):
    if expected == actual:
        raise RuntimeError(f"expected={expected}, actual={actual}")


def assert_true(actual):
    if not actual:
        raise RuntimeError(f"expected True, actual={actual}")


def chunk_list(lst, n):
    return [lst[i:i + n] for i in range(0, len(lst), n)]


def is_empty(item):
    return item is None or len(item) == 0


def not_empty(item):
    return not is_empty(item)


def get_cur_millis():
    return int(time.time() * 1000)


def decode_sse(raw_event_str):
    if not raw_event_str.startswith("data:"):
        return dict()

    return json.loads(raw_event_str[6:])


def get_ending_message(chat_request):
    return chat_request.chat_history[-1].messages[-1]


def may_have_bad_markdown(text):
    if is_empty(text):
        return False

    markdown_pattern = r'(markdown|Markdown|MarkDown)'
    return re.search(markdown_pattern, text) or text.startswith("```")


def pprint(data):
    print("---")
    print(to_json_str(data, intend=2))
    print("---")


def get_env_by_key(key, default=None):
    val = os.environ.get(key)
    if not val:
        logger.warning(f"环境变量 {key} 未设置，将使用默认值 {default}")
        return default

    return val
