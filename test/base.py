import unittest

from config.common_config import get_chat_service_init_param
from util.common_util import get_env_by_key
from direct_chat import MockRequest
from service.chat_service_base import get_chat_service_with_extra


class TestBase(unittest.TestCase):

    def setUp(self):
        ENV_NAME = get_env_by_key("ENV_NAME", "local")
        if ENV_NAME == "local":
            ENV_NAME = "test"  # local uses test environment model config
        init_param = get_chat_service_init_param(ENV_NAME)
        get_chat_service = get_chat_service_with_extra(init_param)
        self.chat_service = get_chat_service(MockRequest(), chat_request)