"""
模型配置文件
"""

from fastapi import HTTPException
from loguru import logger
from prometheus_client import Histogram, Counter

from config.run_config import RUN_CONFIG_DICT, DATA_BASE_HOST, DATA_BASE_USER, DATA_BASE_PASSWORD, DATA_BASE_NAME, \
    DATA_BASE_PORT
from core.schema.chat_service_init_param import ChatServiceInitParam
from data_loader import load_item_name_list, load_item_param_config_dict, load_item_dataset_id_dict, \
    load_item_intro_dict, load_name_item_dict

from config.model_config import MODEL_CONFIG, ModelConfigKey
from service.chat_service_v0 import ChatServiceV0
from service.chat_service_v1 import ChatServiceV1
from service.model_manager import ModelManager
from service.prompt_build_service import PromptBuildService
from service.query_parse_service import QueryParseService
from util.common_util import get_env_by_key
from util.mysql_db_manager import <PERSON>Mana<PERSON>


def get_chat_service_init_param(env_name) -> ChatServiceInitParam:
    cur_config = MODEL_CONFIG[env_name]
    item_name_list, normalized_item_name_list = load_item_name_list(cur_config[ModelConfigKey.ITEM_ID_NAME_PATH])
    item_name_xiaomi_list, normalized_item_name_xiaomi_list = load_item_name_list(
        cur_config[ModelConfigKey.ITEM_ID_NAME_XIAOMI_PATH])
    item_name_dataset_id_dict = load_item_dataset_id_dict(cur_config[ModelConfigKey.ITEM_NAME_DATASET_ID_PATH])
    item_name_intro_dict = load_item_intro_dict(cur_config[ModelConfigKey.MINET_INTRO_PATH])
    item_param_config_dict = load_item_param_config_dict(cur_config[ModelConfigKey.MINET_PARAM_PATH])
    name_item_dict = load_name_item_dict(cur_config[ModelConfigKey.ITEM_ID_NAME_PATH])
    prompt_path = cur_config[ModelConfigKey.PROMPT_PATH]

    endpoint_call_counter = Counter(
        "global_copilot_counter",  # 指标名称
        "Total calls to endpoint",  # 帮助说明
        ["object", "condition"],  # 标签维度（建议控制标签值数量）
    )

    timer_hist = Histogram(
        name="global_copilot_timer",
        documentation="Latency of endpoint calls",
        labelnames=["object", "condition"],
        # 桶边界, 毫秒
        buckets=[0, 50, 100, 150, 200, 400, 800, 1000, 2000, 3000, 4000, 5000, 6000, 7000, 8000, 9000, 10000, 20000,
                 30000],
    )

    return ChatServiceInitParam(
        cur_config, item_name_list, normalized_item_name_list, item_name_xiaomi_list, item_name_dataset_id_dict,
        item_name_intro_dict, item_param_config_dict, name_item_dict, prompt_path, endpoint_call_counter,
        timer_hist
    )


def get_chat_service_list(init_param):
    env_name = get_env_by_key("ENV_NAME", "local")
    if env_name == "local":
        env_name = "test"

    db = DBManager(
        host=RUN_CONFIG_DICT[env_name][DATA_BASE_HOST],
        user=RUN_CONFIG_DICT[env_name][DATA_BASE_USER],
        password=RUN_CONFIG_DICT[env_name][DATA_BASE_PASSWORD],
        database=RUN_CONFIG_DICT[env_name][DATA_BASE_NAME],
        port=int(RUN_CONFIG_DICT[env_name][DATA_BASE_PORT])
    )

    if not db:
        logger.error("DBManager not initialized")
        raise HTTPException(status_code=500, detail="Database manager not initialized")
    model_manager = ModelManager(
        init_param.endpoint_call_counter,
        init_param.timer_hist,
        init_param.cur_config[ModelConfigKey.DATASET_KEY],
    )
    query_parse_service = QueryParseService(
        init_param.cur_config[ModelConfigKey.API_KEY_JSON],
        init_param.cur_config[ModelConfigKey.API_KEY_JSON_MIFY2],
        init_param.cur_config[ModelConfigKey.API_KEY_JSON_MIFY3],
        model_manager,
        init_param.item_name_list,
        init_param.normalized_item_name_list,
        init_param.item_name_xiaomi_list,
        init_param.prompt_path
    )
    prompt_build_service = PromptBuildService(
        init_param.item_param_config_dict,
        model_manager,
        init_param.item_name_dataset_id_dict,
        init_param.item_name_intro_dict,
        init_param.prompt_path
    )

    chat_service_v0 = ChatServiceV0(
        init_param.endpoint_call_counter,
        init_param.timer_hist,
        init_param.cur_config[ModelConfigKey.API_KEY_TEXT],
        query_parse_service,
        prompt_build_service,
        model_manager,
        init_param.normalized_item_name_list,
        init_param.item_name_xiaomi_list,
        db,
        init_param.name_item_dict,
    )

    chat_service_v1 = ChatServiceV1(
        init_param.endpoint_call_counter,
        init_param.timer_hist,
        init_param.cur_config[ModelConfigKey.API_KEY_TEXT],
        query_parse_service,
        prompt_build_service,
        model_manager,
        init_param.normalized_item_name_list,
        init_param.item_name_xiaomi_list,
        db,
        init_param.name_item_dict,
    )
    return [chat_service_v0, chat_service_v1]
